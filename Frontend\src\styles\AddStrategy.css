/* AddStrategy Component Styles */
.AddStrategy {
  width: 100%;
  max-width: 100%;
}

/* Form Section */
.AddStrategy__form {
  display: flex;
  flex-direction: column;
  gap: var(--heading5);
}

.AddStrategy__field {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.AddStrategy__label {
  font-size: var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: var(--smallfont);
}

/* Input Styles */
.AddStrategy__input,
.AddStrategy__select,
.AddStrategy__textarea {
  padding: var(--smallfont) var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
  outline: none;
  font-family: inherit;
}

.AddStrategy__input:focus,
.AddStrategy__select:focus,
.AddStrategy__textarea:focus {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.AddStrategy__input::placeholder,
.AddStrategy__textarea::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.AddStrategy__textarea {
  min-height: 120px;
  resize: vertical;
  line-height: 1.5;
}

.AddStrategy__select {
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right var(--smallfont) center;
  background-size: 16px;
  padding-right: var(--heading4);
}

/* Array Field Styles */
.AddStrategy__array-field {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.AddStrategy__array-input {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
}

.AddStrategy__array-input .AddStrategy__input {
  flex: 1;
}

.AddStrategy__add-btn {
  padding: var(--smallfont);
  background-color: var(--btn-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  height: 40px;
  transition: background-color 0.3s ease;
}

.AddStrategy__add-btn:hover {
  background-color: var(--btn-hover-color);
}

.AddStrategy__array-items {
  display: flex;
  flex-wrap: wrap;
  gap: var(--extrasmallfont);
  margin-top: var(--extrasmallfont);
}

.AddStrategy__array-item {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  background-color: var(--bg-gray);
  padding: var(--extrasmallfont) var(--smallfont);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  color: var(--text-color);
  border: 1px solid var(--light-gray);
}

.AddStrategy__array-item span {
  flex: 1;
}

.AddStrategy__remove-btn {
  background: none;
  border: none;
  color: var(--btn-color);
  cursor: pointer;
  padding: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.AddStrategy__remove-btn:hover {
  background-color: rgba(238, 52, 37, 0.1);
}

/* Rich Text Editor Styles */
.AddStrategy__summernote {
  margin-bottom: var(--smallfont);
}

/* Summernote Editor Wrapper */
.summernote-wrapper {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  font-family: var(--font-family);
  overflow: hidden;
}

.summernote-wrapper:focus-within {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

/* Summernote Toolbar Styling */
.summernote-wrapper .note-toolbar {
  background-color: var(--bg-gray);
  border-bottom: 1px solid var(--light-gray);
  padding: var(--extrasmallfont) var(--smallfont);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.summernote-wrapper .note-toolbar .note-btn-group {
  margin-right: var(--extrasmallfont);
}

.summernote-wrapper .note-toolbar .note-btn {
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  padding: 6px 8px;
  margin: 2px;
  color: var(--text-color);
  font-size: var(--smallfont);
  transition: all 0.2s ease;
}

.summernote-wrapper .note-toolbar .note-btn:hover {
  background-color: var(--white);
  border-color: var(--light-gray);
  color: var(--btn-color);
}

.summernote-wrapper .note-toolbar .note-btn.active {
  background-color: var(--btn-color);
  border-color: var(--btn-color);
  color: var(--white);
}

/* Summernote Editor Area */
.summernote-wrapper .note-editable {
  background-color: var(--white);
  color: var(--text-color);
  font-family: var(--font-family);
  font-size: var(--basefont);
  line-height: 1.6;
  padding: var(--smallfont) var(--basefont);
  border: none;
  outline: none;
}

.summernote-wrapper .note-editable:focus {
  outline: none;
  box-shadow: none;
}

/* Summernote Status Bar */
.summernote-wrapper .note-statusbar {
  background-color: var(--bg-gray);
  border-top: 1px solid var(--light-gray);
  border-radius: 0 0 var(--border-radius) var(--border-radius);
}

/* Legacy rich-text-editor styles for backward compatibility */
.rich-text-editor {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  font-family: var(--font-family);
  overflow: hidden;
}

.rich-text-editor.focused {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.rich-text-toolbar {
  background-color: var(--bg-gray);
  border-bottom: 1px solid var(--light-gray);
  padding: var(--extrasmallfont) var(--smallfont);
  display: flex;
  gap: var(--extrasmallfont);
  flex-wrap: wrap;
}

.toolbar-btn {
  background-color: transparent;
  border: 1px solid transparent;
  color: var(--text-color);
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: var(--smallfont);
}

.toolbar-btn:hover {
  background-color: var(--btn-color);
  color: var(--white);
  border-color: var(--btn-color);
}

.toolbar-btn:active {
  transform: translateY(1px);
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-btn:disabled:hover {
  background-color: transparent;
  color: var(--text-color);
  border-color: transparent;
}

.rich-text-area {
  width: 100%;
  border: none;
  outline: none;
  padding: var(--smallfont) var(--basefont);
  font-size: var(--basefont);
  line-height: 1.6;
  color: var(--text-color);
  font-family: var(--font-family);
  resize: vertical;
  min-height: 150px;
  background-color: var(--white);
}

.rich-text-area::placeholder {
  color: var(--placeholder-color);
  opacity: 0.7;
}

.rich-text-area:focus {
  outline: none;
}

/* Error Display */
.AddStrategy__error {
  background-color: rgba(238, 52, 37, 0.1);
  border: 1px solid var(--btn-color);
  border-radius: var(--border-radius);
  padding: var(--smallfont) var(--basefont);
  margin: var(--smallfont) 0;
}

.AddStrategy__error p {
  color: var(--btn-color);
  margin: 0;
  font-size: var(--smallfont);
  font-weight: 500;
}

/* Loading and Error States */
.AddStrategy .loading-container,
.AddStrategy .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.AddStrategy .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-gray);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.AddStrategy .loading-container p,
.AddStrategy .error-container p {
  color: var(--text-color);
  font-size: var(--basefont);
  margin: 0 0 16px 0;
}

.AddStrategy .error-container h3 {
  color: var(--text-color);
  font-size: var(--heading5);
  margin: 0 0 12px 0;
  font-weight: 600;
}

.AddStrategy .btn {
  padding: 12px 24px;
  border-radius: var(--border-radius);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: var(--basefont);
  text-decoration: none;
  display: inline-block;
}

.AddStrategy .btn-primary {
  background: var(--btn-color);
  color: white;
}

.AddStrategy .btn-primary:hover {
  background: var(--primary-color);
}

/* Header Styles for Edit Strategy */
.AddStrategy__header {
  margin-bottom: var(--heading4);
  text-align: center;
}

.AddStrategy__title {
  font-size: var(--heading4);
  color: var(--text-color);
  font-weight: 600;
  margin: 0 0 var(--smallfont) 0;
}

.AddStrategy__subtitle {
  font-size: var(--basefont);
  color: var(--dark-gray);
  margin: 0;
}

/* Upload Section */
.AddStrategy__upload {
  border: 2px dashed var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--heading4);
  text-align: center;
  background-color: var(--bg-gray);
  transition: all 0.3s ease;
  cursor: pointer;
}

.AddStrategy__upload:hover {
  border-color: var(--btn-color);
  background-color: rgba(238, 52, 37, 0.05);
}

.AddStrategy__upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--smallfont);
}

.AddStrategy__upload-icon {
  font-size: var(--heading3);
  color: var(--dark-gray);
  margin-bottom: var(--smallfont);
}

.AddStrategy__upload-text {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
  margin: 0;
}

.AddStrategy__upload-subtext {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin: 0;
}

/* Action Buttons */
.AddStrategy__actions {
  display: flex;
  gap: var(--basefont);
  justify-content: flex-start;
  margin-top: var(--heading5);
  padding-top: var(--heading5);
  border-top: 1px solid var(--light-gray);
}

.AddStrategy__submit-btn,
.AddStrategy__reset-btn {
  padding: var(--smallfont) var(--heading5);
  font-size: var(--basefont);
  font-weight: 600;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Responsive Design */
@media (max-width: 768px) {
  .AddStrategy__form {
    gap: var(--basefont);
  }

  .AddStrategy__actions {
    flex-direction: column;
    gap: var(--smallfont);
  }

  .AddStrategy__submit-btn,
  .AddStrategy__reset-btn {
    width: 100%;
    justify-content: center;
  }

  .AddStrategy__upload {
    padding: var(--basefont);
  }

  .AddStrategy__upload-icon {
    font-size: var(--heading4);
  }

  .AddStrategy__array-input {
    flex-direction: column;
    align-items: stretch;
  }

  .AddStrategy__add-btn {
    width: 100%;
    justify-content: center;
  }

  .AddStrategy__array-items {
    gap: var(--smallfont);
  }

  .AddStrategy__array-item {
    padding: var(--smallfont);
  }

  /* Rich Text Editor responsive styles */
  .rich-text-toolbar {
    padding: var(--smallfont);
    gap: 4px;
  }

  .toolbar-btn {
    padding: 8px 10px;
    font-size: var(--extrasmallfont);
  }

  .rich-text-area {
    padding: var(--basefont);
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .AddStrategy__label {
    font-size: var(--smallfont);
  }

  .AddStrategy__input,
  .AddStrategy__select,
  .AddStrategy__textarea {
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--smallfont);
  }

  .AddStrategy__upload {
    padding: var(--smallfont);
  }

  .AddStrategy__upload-text {
    font-size: var(--smallfont);
  }

  .AddStrategy__upload-subtext {
    font-size: var(--extrasmallfont);
  }
}
