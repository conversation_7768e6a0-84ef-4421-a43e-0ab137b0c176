import React, { useEffect, useRef } from 'react';

// Import Summernote and its dependencies
import 'summernote/dist/summernote-lite.css';
import 'bootstrap/dist/css/bootstrap.css';

const SummernoteEditor = ({
  value = '',
  onChange,
  placeholder = 'Enter text here...',
  height = 350,
  className = '',
  disabled = false,
  options = {}
}) => {
  const editorRef = useRef(null);
  const summernoteRef = useRef(null);

  useEffect(() => {
    // Dynamically import jQuery and Summernote to avoid SSR issues
    const initializeSummernote = async () => {
      try {
        // Import jQuery
        const $ = (await import('jquery')).default;

        // Make jQuery available globally for Summernote
        window.jQuery = $;
        window.$ = $;

        // Import Summernote
        await import('summernote/dist/summernote-lite.js');

        // Initialize Summernote
        if (editorRef.current && !summernoteRef.current) {
          const defaultOptions = {
            height: height,
            placeholder: placeholder,
            toolbar: [
              ['style', ['style']],
              ['font', ['bold', 'underline', 'clear']],
              ['fontname', ['fontname']],
              ['para', ['ul', 'ol', 'paragraph']],
              ['table', ['table']],
              ['insert', ['link', 'picture', 'video']],
              ['view', ['fullscreen', 'codeview']]
            ],
            callbacks: {
              onChange: function(contents) {
                if (onChange) {
                  onChange(contents);
                }
              }
            },
            ...options
          };

          $(editorRef.current).summernote(defaultOptions);
          summernoteRef.current = $(editorRef.current);

          // Set initial value
          if (value) {
            $(editorRef.current).summernote('code', value);
          }

          // Handle disabled state
          if (disabled) {
            $(editorRef.current).summernote('disable');
          }
        }
      } catch (error) {
        console.error('Failed to initialize Summernote:', error);
      }
    };

    initializeSummernote();

    // Cleanup function
    return () => {
      if (summernoteRef.current) {
        try {
          summernoteRef.current.summernote('destroy');
          summernoteRef.current = null;
        } catch (error) {
          console.error('Error destroying Summernote:', error);
        }
      }
    };
  }, []);

  // Update content when value prop changes
  useEffect(() => {
    if (summernoteRef.current && value !== undefined) {
      const currentContent = summernoteRef.current.summernote('code');
      if (currentContent !== value) {
        summernoteRef.current.summernote('code', value);
      }
    }
  }, [value]);

  // Update disabled state
  useEffect(() => {
    if (summernoteRef.current) {
      if (disabled) {
        summernoteRef.current.summernote('disable');
      } else {
        summernoteRef.current.summernote('enable');
      }
    }
  }, [disabled]);

  return (
    <div className={`summernote-wrapper ${className}`}>
      <div ref={editorRef}></div>
    </div>
  );
};

export default SummernoteEditor;
