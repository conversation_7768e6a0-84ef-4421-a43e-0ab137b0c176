import React from "react";
import { useSelector } from "react-redux";
import { selectMyBids } from "../../redux/slices/buyerDashboardSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import { FaGavel, FaEye } from "react-icons/fa";
import Table from "../../components/common/Table";
import "../../styles/BuyerBids.css";

const BuyerBids = () => {
  const bids = useSelector(selectMyBids);

  const columns = [
    {
      key: "no",
      label: "No.",
      className: "no",
    },
    {
      key: "bidId",
      label: "Bid Id",
      className: "bid-id",
    },
    {
      key: "video",
      label: "Videos/Documents",
      className: "video",
    },
    {
      key: "date",
      label: "Date",
      className: "date",
    },
    {
      key: "bidAmount",
      label: "Bid Amount",
      className: "bid-amount",
    },
    {
      key: "status",
      label: "Status",
      className: "status",
    },
    {
      key: "action",
      label: "Action",
      className: "action",
    },
  ];

  const renderRow = (bid, index) => (
    <>
      <div className="table-cell no">{index + 1}</div>
      <div className="table-cell bid-id">#12245578</div>
      <div className="table-cell video">
        <div className="content-item">
          <div className="content-image">
            <img
              src="https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop"
              alt={bid.title}
            />
          </div>
          <div className="content-info">
            <div className="content-title">{bid.title}</div>
            <div className="content-coach">By {bid.coach}</div>
          </div>
        </div>
      </div>
      <div className="table-cell date">{bid.date} | 4:30PM</div>
      <div className="table-cell bid-amount">${bid.bidAmount.toFixed(2)}</div>
      <div className="table-cell status">
        <span className={`status-badge ${bid.status}`}>
          {bid.status.charAt(0).toUpperCase() + bid.status.slice(1)}
        </span>
      </div>
      <div className="table-cell action">
        <button className="action-btn">
          <FaEye />
        </button>
      </div>
    </>
  );

  return (
    <div className="BuyerBids">
      <SectionWrapper
        icon={<FaGavel className="BuyerSidebar__icon" />}
        title="My Bids"
      >
        {bids.length > 0 ? (
          <Table
            columns={columns}
            data={bids}
            renderRow={renderRow}
            variant="grid"
            gridTemplate="0.5fr 1fr 3fr 1.5fr 1fr 1fr 0.5fr"
            className="BuyerBids__table"
            emptyMessage="You have no bids yet."
          />
        ) : (
          <div className="BuyerBids__empty">
            <p>You have no bids yet.</p>
          </div>
        )}
      </SectionWrapper>
    </div>
  );
};

export default BuyerBids;
