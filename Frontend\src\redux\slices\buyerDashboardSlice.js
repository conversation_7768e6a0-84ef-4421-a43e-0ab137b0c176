import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { strategyData } from "../../data/strategyData";
import dashboardService from "../../services/dashboardService";
import { getAllContent } from "../../services/contentService";
import { getBuyerOrders } from "../../services/orderService";
import { getUserBids } from "../../services/bidService";

// Async Thunks for API calls
export const fetchBuyerDashboardStats = createAsyncThunk(
  'buyerDashboard/fetchStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await dashboardService.getBuyerDashboardStats();
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch dashboard stats');
    }
  }
);

export const fetchBuyerOrders = createAsyncThunk(
  'buyerDashboard/fetchOrders',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await getBuyerOrders(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch orders');
    }
  }
);

export const fetchBuyerDownloads = createAsyncThunk(
  'buyerDashboard/fetchDownloads',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await dashboardService.getBuyerDownloads(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch downloads');
    }
  }
);

export const fetchBuyerBids = createAsyncThunk(
  'buyerDashboard/fetchBids',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await getUserBids(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch bids');
    }
  }
);

export const fetchBuyerRequests = createAsyncThunk(
  'buyerDashboard/fetchRequests',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await dashboardService.getBuyerRequests(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch requests');
    }
  }
);

export const fetchAllStrategies = createAsyncThunk(
  'buyerDashboard/fetchStrategies',
  async (params = {}, { rejectWithValue }) => {
    try {
      const response = await getAllContent(params);
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch strategies');
    }
  }
);

export const fetchBuyerNotifications = createAsyncThunk(
  'buyerDashboard/fetchNotifications',
  async (_, { rejectWithValue }) => {
    try {
      const response = await dashboardService.getBuyerNotifications();
      return response;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch notifications');
    }
  }
);

// Initial state for the buyer dashboard
const initialState = {
  // Sidebar state
  activeTab: "dashboard", // Default active tab
  isSidebarOpen: false,

  // Loading states
  loading: {
    stats: false,
    orders: false,
    downloads: false,
    bids: false,
    requests: false,
    strategies: false,
    notifications: false,
  },

  // Error states
  errors: {
    stats: null,
    orders: null,
    downloads: null,
    bids: null,
    requests: null,
    strategies: null,
    notifications: null,
  },

  // User profile data (mock data for development)
  profile: {
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "+****************",
    profileImage: null,
  },

  // Dashboard stats
  stats: {
    totalDownloads: 0,
    totalRequests: 0,
    totalBids: 0,
    totalSpent: 0,
    activeOrders: 0,
    completedOrders: 0,
  },

  // Dashboard data
  myCards: [
    { id: "1", lastFourDigits: "1234", cardType: "mastercard" },
    { id: "2", lastFourDigits: "5678", cardType: "mastercard" },
    { id: "3", lastFourDigits: "9012", cardType: "mastercard" },
  ],
  myBids: [
    {
      id: "1",
      title: "Advanced Basketball Strategies",
      coach: "Michael Johnson",
      bidAmount: 75.0,
      date: "2023-05-15",
      status: "active",
    },
    {
      id: "2",
      title: "Football Defense Techniques",
      coach: "Robert Williams",
      bidAmount: 60.0,
      date: "2023-05-10",
      status: "won",
    },
    {
      id: "3",
      title: "Soccer Goal Keeping Masterclass",
      coach: "David Martinez",
      bidAmount: 85.0,
      date: "2023-05-05",
      status: "lost",
    },
  ],
  myDownloads: [
    {
      id: "1",
      title: "Basketball Offense Strategies",
      coach: "John Smith",
      downloadDate: "2023-05-20",
      fileSize: "15.2 MB",
      fileType: "PDF",
    },
    {
      id: "2",
      title: "Football Training Drills",
      coach: "Mike Johnson",
      downloadDate: "2023-05-18",
      fileSize: "25.6 MB",
      fileType: "Video",
    },
    {
      id: "3",
      title: "Tennis Serve Techniques",
      coach: "Sarah Williams",
      downloadDate: "2023-05-15",
      fileSize: "10.8 MB",
      fileType: "PDF",
    },
  ],
  myRequests: [
    {
      id: "1",
      title: "Advanced Swimming Techniques",
      description:
        "Looking for professional swimming techniques for competitive swimmers",
      date: "2023-05-22",
      status: "pending",
    },
    {
      id: "2",
      title: "Golf Swing Analysis",
      description: "Need a detailed analysis of golf swing mechanics",
      date: "2023-05-19",
      status: "approved",
    },
    {
      id: "3",
      title: "Marathon Training Plan",
      description: "Request for a 16-week marathon training plan for beginners",
      date: "2023-05-17",
      status: "completed",
    },
  ],

  // Card UI state
  cardUI: {
    viewMode: "list", // 'list' or 'add'
  },

  // Card form state
  cardForm: {
    nameOnCard: "",
    cardNumber: "",
    expiryDate: "",
    cvv: "",
  },

  strategies: strategyData,

  // Notifications
  notifications: [],
  unreadNotificationsCount: 0,

  // Pagination and filtering
  pagination: {
    orders: { page: 1, limit: 10, total: 0 },
    downloads: { page: 1, limit: 10, total: 0 },
    bids: { page: 1, limit: 10, total: 0 },
    requests: { page: 1, limit: 10, total: 0 },
    strategies: { page: 1, limit: 12, total: 0 },
  },

  // Filters
  filters: {
    orders: {},
    downloads: {},
    bids: {},
    requests: {},
    strategies: {
      sport: "Baseball",
      category: [],
      priceRange: [0, 1000],
      sortBy: "price_desc",
    },
  },

  // Search
  searchTerms: {
    orders: "",
    downloads: "",
    bids: "",
    requests: "",
    strategies: "",
  },
};

const buyerDashboardSlice = createSlice({
  name: "buyerDashboard",
  initialState,
  reducers: {
    // Sidebar actions
    setActiveTab: (state, action) => {
      state.activeTab = action.payload;
    },
    toggleSidebar: (state) => {
      state.isSidebarOpen = !state.isSidebarOpen;
    },
    openSidebar: (state) => {
      state.isSidebarOpen = true;
    },
    closeSidebar: (state) => {
      state.isSidebarOpen = false;
    },

    // Profile actions
    updateProfile: (state, action) => {
      state.profile = { ...state.profile, ...action.payload };
    },
    updateProfileImage: (state, action) => {
      state.profile.profileImage = action.payload;
    },

    // Dashboard data actions
    setMyCards: (state, action) => {
      state.myCards = action.payload;
    },
    addCard: (state, action) => {
      state.myCards.push(action.payload);
    },
    removeCard: (state, action) => {
      state.myCards = state.myCards.filter(
        (card) => card.id !== action.payload
      );
    },
    setMyBids: (state, action) => {
      state.myBids = action.payload;
    },
    setMyDownloads: (state, action) => {
      state.myDownloads = action.payload;
    },
    setMyRequests: (state, action) => {
      state.myRequests = action.payload;
    },

    // Card UI actions
    setCardViewMode: (state, action) => {
      state.cardUI.viewMode = action.payload;
    },

    // Card form actions
    updateCardForm: (state, action) => {
      state.cardForm = { ...state.cardForm, ...action.payload };
    },
    resetCardForm: (state) => {
      state.cardForm = initialState.cardForm;
    },

    // Pagination actions
    updatePagination: (state, action) => {
      const { section, pagination } = action.payload;
      state.pagination[section] = { ...state.pagination[section], ...pagination };
    },

    // Filter actions
    updateFilters: (state, action) => {
      const { section, filters } = action.payload;
      state.filters[section] = { ...state.filters[section], ...filters };
    },

    // Search actions
    updateSearchTerm: (state, action) => {
      const { section, searchTerm } = action.payload;
      state.searchTerms[section] = searchTerm;
    },

    // Clear errors
    clearError: (state, action) => {
      const section = action.payload;
      state.errors[section] = null;
    },

    // Clear all errors
    clearAllErrors: (state) => {
      state.errors = {
        stats: null,
        orders: null,
        downloads: null,
        bids: null,
        requests: null,
        strategies: null,
        notifications: null,
      };
    },
  },
  extraReducers: (builder) => {
    // Dashboard Stats
    builder
      .addCase(fetchBuyerDashboardStats.pending, (state) => {
        state.loading.stats = true;
        state.errors.stats = null;
      })
      .addCase(fetchBuyerDashboardStats.fulfilled, (state, action) => {
        state.loading.stats = false;
        state.stats = action.payload;
      })
      .addCase(fetchBuyerDashboardStats.rejected, (state, action) => {
        state.loading.stats = false;
        state.errors.stats = action.payload;
      });

    // Orders
    builder
      .addCase(fetchBuyerOrders.pending, (state) => {
        state.loading.orders = true;
        state.errors.orders = null;
      })
      .addCase(fetchBuyerOrders.fulfilled, (state, action) => {
        state.loading.orders = false;
        state.myOrders = action.payload.orders || action.payload;
        if (action.payload.pagination) {
          state.pagination.orders = action.payload.pagination;
        }
      })
      .addCase(fetchBuyerOrders.rejected, (state, action) => {
        state.loading.orders = false;
        state.errors.orders = action.payload;
      });

    // Downloads
    builder
      .addCase(fetchBuyerDownloads.pending, (state) => {
        state.loading.downloads = true;
        state.errors.downloads = null;
      })
      .addCase(fetchBuyerDownloads.fulfilled, (state, action) => {
        state.loading.downloads = false;
        state.myDownloads = action.payload.downloads || action.payload;
        if (action.payload.pagination) {
          state.pagination.downloads = action.payload.pagination;
        }
      })
      .addCase(fetchBuyerDownloads.rejected, (state, action) => {
        state.loading.downloads = false;
        state.errors.downloads = action.payload;
      });

    // Bids
    builder
      .addCase(fetchBuyerBids.pending, (state) => {
        state.loading.bids = true;
        state.errors.bids = null;
      })
      .addCase(fetchBuyerBids.fulfilled, (state, action) => {
        state.loading.bids = false;
        state.myBids = action.payload.bids || action.payload;
        if (action.payload.pagination) {
          state.pagination.bids = action.payload.pagination;
        }
      })
      .addCase(fetchBuyerBids.rejected, (state, action) => {
        state.loading.bids = false;
        state.errors.bids = action.payload;
      });

    // Requests
    builder
      .addCase(fetchBuyerRequests.pending, (state) => {
        state.loading.requests = true;
        state.errors.requests = null;
      })
      .addCase(fetchBuyerRequests.fulfilled, (state, action) => {
        state.loading.requests = false;
        state.myRequests = action.payload.requests || action.payload;
        if (action.payload.pagination) {
          state.pagination.requests = action.payload.pagination;
        }
      })
      .addCase(fetchBuyerRequests.rejected, (state, action) => {
        state.loading.requests = false;
        state.errors.requests = action.payload;
      });

    // Strategies
    builder
      .addCase(fetchAllStrategies.pending, (state) => {
        state.loading.strategies = true;
        state.errors.strategies = null;
      })
      .addCase(fetchAllStrategies.fulfilled, (state, action) => {
        state.loading.strategies = false;
        state.strategies = action.payload.content || action.payload;
        if (action.payload.pagination) {
          state.pagination.strategies = action.payload.pagination;
        }
      })
      .addCase(fetchAllStrategies.rejected, (state, action) => {
        state.loading.strategies = false;
        state.errors.strategies = action.payload;
      });

    // Notifications
    builder
      .addCase(fetchBuyerNotifications.pending, (state) => {
        state.loading.notifications = true;
        state.errors.notifications = null;
      })
      .addCase(fetchBuyerNotifications.fulfilled, (state, action) => {
        state.loading.notifications = false;
        state.notifications = action.payload.notifications || action.payload;
        state.unreadNotificationsCount = action.payload.unreadCount || 0;
      })
      .addCase(fetchBuyerNotifications.rejected, (state, action) => {
        state.loading.notifications = false;
        state.errors.notifications = action.payload;
      });
  },
});

// Export actions
export const {
  setActiveTab,
  toggleSidebar,
  openSidebar,
  closeSidebar,
  updateProfile,
  updateProfileImage,
  setMyCards,
  addCard,
  removeCard,
  setMyBids,
  setMyDownloads,
  setMyRequests,
  setCardViewMode,
  updateCardForm,
  resetCardForm,
  updatePagination,
  updateFilters,
  updateSearchTerm,
  clearError,
  clearAllErrors,
} = buyerDashboardSlice.actions;

// Export selectors
export const selectActiveTab = (state) => state.buyerDashboard.activeTab;
export const selectIsSidebarOpen = (state) =>
  state.buyerDashboard.isSidebarOpen;
export const selectProfile = (state) => state.buyerDashboard.profile;
export const selectMyCards = (state) => state.buyerDashboard.myCards;
export const selectMyBids = (state) => state.buyerDashboard.myBids;
export const selectMyDownloads = (state) => state.buyerDashboard.myDownloads;
export const selectMyRequests = (state) => state.buyerDashboard.myRequests;
export const selectCardViewMode = (state) =>
  state.buyerDashboard.cardUI.viewMode;
export const selectCardForm = (state) => state.buyerDashboard.cardForm;

// Add selectors for strategies
export const selectAllStrategies = (state) => state.buyerDashboard.strategies;
export const selectStrategyById = (state, id) =>
  state.buyerDashboard.strategies.find((s) => String(s.id) === String(id));

// Loading selectors
export const selectLoading = (state) => state.buyerDashboard.loading;
export const selectLoadingBySection = (state, section) => state.buyerDashboard.loading[section];

// Error selectors
export const selectErrors = (state) => state.buyerDashboard.errors;
export const selectErrorBySection = (state, section) => state.buyerDashboard.errors[section];

// Stats selectors
export const selectDashboardStats = (state) => state.buyerDashboard.stats;

// Pagination selectors
export const selectPagination = (state) => state.buyerDashboard.pagination;
export const selectPaginationBySection = (state, section) => state.buyerDashboard.pagination[section];

// Filter selectors
export const selectFilters = (state) => state.buyerDashboard.filters;
export const selectFiltersBySection = (state, section) => state.buyerDashboard.filters[section];

// Search selectors
export const selectSearchTerms = (state) => state.buyerDashboard.searchTerms;
export const selectSearchTermBySection = (state, section) => state.buyerDashboard.searchTerms[section];

// Notification selectors
export const selectNotifications = (state) => state.buyerDashboard.notifications;
export const selectUnreadNotificationsCount = (state) => state.buyerDashboard.unreadNotificationsCount;

// Export reducer
export default buyerDashboardSlice.reducer;
